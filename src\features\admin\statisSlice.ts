import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import api from "../../config/axiosConfig.tsx";

const URL = '/statistics';

const initialState = {
    subscribersByTrip: [],
    subscribersByEstablishment: [],
    subscribersByLineKmRange: [],
    loading: false,
    error: null,
    currentItem: null
};

export const getSubscribersByTrip: any = createAsyncThunk(
    "getSubscribersByTrip",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        journey?: string;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-trip`;
            const searchParams = [];

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscription_type=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`start_date=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`end_date=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`sale_period=${params.salePeriod}`);
            }
            if (params.journey && params.journey !== 'all') {
                searchParams.push(`trip=${params.journey}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByEstablishment: any = createAsyncThunk(
    "getSubscribersByEstablishment",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        establishment?: string;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-establishment`;
            const searchParams = [];

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscription_type=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`start_date=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`end_date=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`sale_period=${params.salePeriod}`);
            }
            if (params.establishment && params.establishment !== 'all') {
                searchParams.push(`establishment=${params.establishment}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

export const getSubscribersByLineKmRange: any = createAsyncThunk(
    "getSubscribersByLineKmRange",
    async (params: {
        subscriptionType?: string;
        startDate?: string;
        endDate?: string;
        salePeriod?: string;
        lineId?: string;
    }, thunkAPI: any) => {
        try {
            let url: string = `${URL}/subscribers-by-kilometric-range-per-line`;
            const searchParams = [];

            if (params.subscriptionType && params.subscriptionType !== 'all') {
                searchParams.push(`subscriptionType=${params.subscriptionType}`);
            }
            if (params.startDate) {
                searchParams.push(`startDate=${params.startDate}`);
            }
            if (params.endDate) {
                searchParams.push(`endDate=${params.endDate}`);
            }
            if (params.salePeriod && params.salePeriod !== 'all') {
                searchParams.push(`salePeriod=${params.salePeriod}`);
            }
            if (params.lineId && params.lineId !== 'all') {
                searchParams.push(`lineId=${params.lineId}`);
            }

            if (searchParams.length > 0) {
                url += `?${searchParams.join('&')}`;
            }

            const resp: any = await api.get(url);
            return resp.data;
        } catch (error: any) {
            if (error.response) {
                const { status, data } = error.response;
                if (status === 403) {
                    window.location.href = "/unauthorized";
                }
                return thunkAPI.rejectWithValue(data?.message || "An error occurred.");
            } else {
                return thunkAPI.rejectWithValue("An unexpected error occurred.");
            }
        }
    }
);

const statisSlice = createSlice({
    name: 'statistics',
    initialState,
    reducers: {
        setCurrentItem: (state, action) => {
            state.currentItem = action.payload;
        },
        clearError: (state) => {
            state.error = null;
        },
        clearSubscribersByTrip: (state) => {
            state.subscribersByTrip = [];
        },
        clearSubscribersByEstablishment: (state) => {
            state.subscribersByEstablishment = [];
        },
        clearSubscribersByLineKmRange: (state) => {
            state.subscribersByLineKmRange = [];
        }
    },
    extraReducers: (builder) => {
        builder
            // getSubscribersByTrip
            .addCase(getSubscribersByTrip.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByTrip.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByTrip = action.payload;
            })
            .addCase(getSubscribersByTrip.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByEstablishment
            .addCase(getSubscribersByEstablishment.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByEstablishment.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByEstablishment = action.payload;
            })
            .addCase(getSubscribersByEstablishment.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
            // getSubscribersByLineKmRange
            .addCase(getSubscribersByLineKmRange.pending, (state) => {
                state.loading = true;
                state.error = null;
            })
            .addCase(getSubscribersByLineKmRange.fulfilled, (state, action) => {
                state.loading = false;
                state.subscribersByLineKmRange = action.payload;
            })
            .addCase(getSubscribersByLineKmRange.rejected, (state: any, action) => {
                state.loading = false;
                state.error = action.payload as string;
            })
    }
});

export const { setCurrentItem, clearError, clearSubscribersByTrip, clearSubscribersByEstablishment, clearSubscribersByLineKmRange } = statisSlice.actions;
export default statisSlice.reducer;
