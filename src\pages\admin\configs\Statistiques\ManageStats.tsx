import React, { useState, useEffect } from "react";
import {
  Select,
  Table,
  Form,
  DatePicker,
  Button,
  Input,
  Breadcrumb,
  <PERSON><PERSON>,
  Spin,
  Row,
  Col,
  Typography,
  Collapse,
} from "antd";
import {
  SettingOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  FilterOutlined,
  UserOutlined,
  DollarOutlined,
  DatabaseOutlined,
  TrophyOutlined
} from "@ant-design/icons";
import { Column, Pie, Line } from "@ant-design/charts";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { getSubscribersByTrip, clearSubscribersByTrip, getSubscribersByEstablishment, clearSubscribersByEstablishment, getSubscribersByLineKmRange, clearSubscribersByLineKmRange, getSubscribersByDiscount, clearSubscribersByDiscount, getRevenuesBySalePeriod, clearRevenuesBySalePeriod } from "../../../../features/admin/statisSlice";
import { getAbnTypesAll } from "../../../../features/admin/abnTypeSlice";
import { getTripsAll } from "../../../../features/admin/tripsSlice";
import { getSalesPeriodsAll } from "../../../../features/admin/salesPeriodsSlice";
import { getEstablishmentAll } from "../../../../features/admin/establishmentSlice";
import { getLinesAll } from "../../../../features/admin/lineSlice";
import { getDiscountsAll } from "../../../../features/admin/discountSlice";
import dayjs from "dayjs";

const { Option } = Select;
const { Title } = Typography;
const { Panel } = Collapse;

const ManageStats: React.FC = () => {
  const [form] = Form.useForm();
  const { t, i18n } = useTranslation();
  const currentLang = i18n.language;

  // Obtenir le nom du mois précédent
  const getLastMonthName = () => {
    const lastMonth = dayjs().subtract(1, 'month');
    if (currentLang === 'fr') {
      return lastMonth.locale('fr').format('MMMM YYYY');
    } else if (currentLang === 'en') {
      return lastMonth.locale('en').format('MMMM YYYY');
    } else if (currentLang === 'ar') {
      return lastMonth.locale('ar').format('MMMM YYYY');
    } else {
      return lastMonth.format('MMMM YYYY');
    }
  };
  const dispatch = useDispatch();
  const [selectedStatistic, setSelectedStatistic] = useState<string>("1");
  const [chartType, setChartType] = useState<'column' | 'pie' | 'line'>('column');

  const { subscribersByTrip, subscribersByEstablishment, subscribersByLineKmRange, subscribersByDiscount, revenuesBySalePeriod, loading, error } = useSelector((state: any) => state.statis);
  const abnTypes  = useSelector((state: any) => state.abnType.items.data);
  const trips  = useSelector((state: any) => state.trips.items.data);
  const salesPeriods  = useSelector((state: any) => state.salesPeriod.items.data);
  const establishments = useSelector((state: any) => state.establishment.items.data);
  const lines = useSelector((state: any) => state.line.items.data);
  const discounts = useSelector((state: any) => state.discount.items.data);


  const fetchStoreData = async () => {
            if(!abnTypes?.length){
                await dispatch(getAbnTypesAll()).unwrap()
            }
            if(!trips?.length){
                await dispatch(getTripsAll()).unwrap()
            }
            if(!salesPeriods?.length){
                await dispatch(getSalesPeriodsAll()).unwrap()
            }
            if(!establishments?.length){
                await dispatch(getEstablishmentAll()).unwrap()
            }
             if(!lines?.length){
                await dispatch(getLinesAll()).unwrap()
            }
             if(!discounts?.length){
                await dispatch(getDiscountsAll()).unwrap()
            }
        }



  useEffect(() => {
    fetchStoreData()
    dispatch(getSubscribersByTrip({}));
  }, [dispatch]);

  const calculateSummaryStats = () => {
    if (selectedStatistic === "3") {
      return calculateLineKmSummaryStats();
    }
    if (selectedStatistic === "4") {
      return calculateDiscountSummaryStats();
    }
    if (selectedStatistic === "5") {
      return calculateSalePeriodSummaryStats();
    }

    // Pour les statistiques 1 et 2, utiliser les données summary.all_times
    if (selectedStatistic === "1" && subscribersByTrip?.summary) {
      const summary = subscribersByTrip.summary.all_times;
      return {
        totalSubscribers: summary.total_subscribers || 0,
        totalAmount: summary.total_amount || 0,
        totalItems: summary.total_trips || 0,
        averagePerItem: summary.total_trips > 0 ? (summary.total_amount || 0) / summary.total_trips : 0
      };
    }

    if (selectedStatistic === "2" && subscribersByEstablishment?.summary) {
      const summary = subscribersByEstablishment.summary.all_times;
      return {
        totalSubscribers: summary.total_subscribers || 0,
        totalAmount: summary.total_amount || 0,
        totalItems: summary.total_establishments || 0,
        averagePerItem: summary.total_establishments > 0 ? (summary.total_amount || 0) / summary.total_establishments : 0
      };
    }

    return {
      totalSubscribers: 0,
      totalAmount: 0,
      totalItems: 0,
      averagePerItem: 0
    };
  };

  const calculateDiscountChartSummaryStats = () => {
    if (!subscribersByDiscount?.summary) {
      return {
        totalSubscribers: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByDiscount.summary.last_month;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_discounts || 0,
      averagePerItem: summary.total_discounts > 0 ? (summary.total_montant || 0) / summary.total_discounts : 0
    };
  };

  const prepareChartData = () => {
    if (selectedStatistic === "3") {
      return prepareLineKmChartData();
    }
    if (selectedStatistic === "4") {
      return prepareDiscountChartData();
    }
    if (selectedStatistic === "5") {
      return prepareSalePeriodChartData();
    }

    let currentData;
    if (selectedStatistic === "1") {
      currentData = subscribersByTrip?.data;
    } else if (selectedStatistic === "2") {
      currentData = subscribersByEstablishment?.data;
    }

    if (!currentData?.length) return [];

    if (selectedStatistic === "1") {
      return currentData.map((item: any) => ({
        nom: item[`nom_${currentLang}`] || item.trip_name || `Trajet ${item.trip_id}`,
        abonnes: item.last_month?.subscriber_count || 0,
        montant: item.last_month?.total_amount || 0,
      }));
    } else if (selectedStatistic === "2") {
      return currentData.map((item: any) => ({
        nom: item[`nom_${currentLang}`] || item.establishment_name || `Établissement ${item.establishment_id}`,
        abonnes: item.last_month?.subscriber_count || 0,
        montant: item.last_month?.total_amount || 0,
      }));
    }

    return [];
  };

  const getSubscriptionTypeOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    abnTypes?.forEach((type: any) => {
      options.push({
        value: type.id.toString(),
        label: type.nom_fr || type.nom_en || type.nom_ar
      });
    });
    return options;
  };

  const getTripOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    trips?.forEach((trip: any) => {
      options.push({
        value: trip.id.toString(),
        label: trip.nom_fr || trip.nom_en || trip.nom_ar
      });
    });
    return options;
  };

  const getSalePeriodOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    salesPeriods?.forEach((period: any) => {
      options.push({
        value: period.id.toString(),
        label: period.nom_fr || period.nom_en || period.nom_ar
      });
    });
    return options;
  };

  const getEstablishmentOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    establishments?.forEach((establishment: any) => {
      options.push({
        value: establishment.id.toString(),
        label: establishment.nom_fr || establishment.nom_en || establishment.nom_ar
      });
    });
    return options;
  };

  const getLineOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    lines?.forEach((line: any) => {
      options.push({
        value: line.id.toString(),
        label: line.nom_fr || line.nom_en || line.nom_ar
      });
    });
    return options;
  };

  const getDiscountOptions = () => {
    const options = [{ value: "all", label: t("common.all") }];
    discounts?.forEach((discount: any) => {
      options.push({
        value: discount.id.toString(),
        label: `${discount.nom_fr || discount.nom_en || discount.nom_ar} (${discount.percentage}%)`
      });
    });
    return options;
  };

  const STATISTICS_CONFIG: Record<string, any> = {
    "1": {
      id: "1",
      label: "manage_stats.abonnees_trajet",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "journey",
          label: "manage_stats.trajet",
          type: "select",
          options: getTripOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.trajet"),
          dataIndex: `nom_${currentLang}`,
          key: "trip_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || '-',
        },
        {
          title: t("manage_stats.nb_abonnees") + " (Total)",
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_abonnees") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "subscriber_count"],
          key: "subscriber_count_month",
          render: (_: string, record: any) => record?.last_month?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.montant") + " (Total)",
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.montant") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "total_amount"],
          key: "total_amount_month",
          render: (_: string, record: any) => `${record?.last_month?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "2": {
      id: "2",
      label: "manage_stats.sub_etab",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "establishment",
          label: "manage_stats.etab",
          type: "select",
          options: getEstablishmentOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.etab"),
          dataIndex: `nom_${currentLang}`,
          key: "establishment_name",
          render: (_: string, record: any) => record?.[`nom_${currentLang}`] || record?.establishment_name || '-',
        },
        {
          title: t("manage_stats.nb_abonnees") + " (Total)",
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_abonnees") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "subscriber_count"],
          key: "subscriber_count_month",
          render: (_: string, record: any) => record?.last_month?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.montant") + " (Total)",
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.montant") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "total_amount"],
          key: "total_amount_month",
          render: (_: string, record: any) => `${record?.last_month?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "3": {
      id: "3",
      label: "manage_stats.sub_trancheKm_ligne",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
        {
          name: "salePeriod",
          label: "manage_stats.period",
          type: "select",
          options: getSalePeriodOptions(),
        },
        {
          name: "lineId",
          label: "manage_stats.ligne",
          type: "select",
          options: getLineOptions(),
        },
      ],
      columns: [
        {
          title: t("manage_stats.ligne"),
          dataIndex: `line_name_${currentLang}`,
          key: "line_name",
          render: (_: string, record: any) => record?.[`line_name_${currentLang}`] || record?.line_code || '-',
        },
        {
          title: t("manage_stats.tranch_km"),
          dataIndex: "kilometric_range",
          key: "kilometric_range",
        },
        {
          title: t("manage_stats.nb_abonnees") + " (Total)",
          dataIndex: ["all_times", "subscriber_count"],
          key: "subscriber_count_total",
          render: (_: string, record: any) => record?.all_times?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.nb_abonnees") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "subscriber_count"],
          key: "subscriber_count_month",
          render: (_: string, record: any) => record?.last_month?.subscriber_count || 0,
        },
        {
          title: t("manage_stats.montant") + " (Total)",
          dataIndex: ["all_times", "total_amount"],
          key: "total_amount_all",
          render: (_: string, record: any) => `${record?.all_times?.total_amount?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.montant") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "total_amount"],
          key: "total_amount_month",
          render: (_: string, record: any) => `${record?.last_month?.total_amount?.toFixed(2) || 0} DT`
        },
      ],
    },

    "4": {
      id: "4",
      label: "manage_stats.abonnees_remise",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.remise"),
          dataIndex: `nom_${currentLang}`,
          key: "discount_name",
          render: (_: string, record: any) => {
            const name = record?.[`nom_${currentLang}`] || record?.nom_fr || '-';
            const percentage = record?.percentage ? ` (${record.percentage}%)` : '';
            return `${name}${percentage}`;
          },
        },
        {
          title: t("manage_stats.nb_abonnements") + " (Total)",
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.nb_abonnements") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "nombre_abonnements"],
          key: "nombre_abonnements_month",
          render: (_: string, record: any) => record?.last_month?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.montant") + " (Total)",
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.montant") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "montant_total"],
          key: "montant_total_month",
          render: (_: string, record: any) => `${record?.last_month?.montant_total?.toFixed(2) || 0} DT`
        },
      ],
    },

    "5": {
      id: "5",
      label: "manage_stats.recettes_periode_vente",
      filters: [
        {
          name: "subscriptionType",
          label: "manage_stats.type_abn",
          type: "select",
          options: getSubscriptionTypeOptions(),
        },
        {
          name: "startDate",
          label: "manage_stats.start_date",
          type: "date",
        },
        {
          name: "endDate",
          label: "manage_stats.end_date",
          type: "date",
        },
      ],
      columns: [
        {
          title: t("manage_stats.campagne"),
          dataIndex: `campaign_nom_${currentLang}`,
          key: "campaign_name",
          render: (_: string, record: any) => record?.[`campaign_nom_${currentLang}`] || record?.campaign_nom_fr || '-',
        },
        {
          title: t("manage_stats.periode_vente"),
          dataIndex: `sale_period_nom_${currentLang}`,
          key: "sale_period_name",
          render: (_: string, record: any) => {
            const name = record?.[`sale_period_nom_${currentLang}`] || record?.sale_period_nom_fr || '-';
            const dateStart = record?.date_start ? dayjs(record.date_start).format('DD/MM/YYYY') : '';
            const dateEnd = record?.date_end ? dayjs(record.date_end).format('DD/MM/YYYY') : '';
            const dates = dateStart && dateEnd ? ` (${dateStart} - ${dateEnd})` : '';
            return `${name}${dates}`;
          },
        },
        {
          title: t("manage_stats.nb_transactions") + " (Total)",
          dataIndex: ["all_times", "nombre_transactions"],
          key: "nombre_transactions_total",
          render: (_: string, record: any) => record?.all_times?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.nb_transactions") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "nombre_transactions"],
          key: "nombre_transactions_month",
          render: (_: string, record: any) => record?.last_month?.nombre_transactions || 0,
        },
        {
          title: t("manage_stats.nb_abonnements") + " (Total)",
          dataIndex: ["all_times", "nombre_abonnements"],
          key: "nombre_abonnements_total",
          render: (_: string, record: any) => record?.all_times?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.nb_abonnements") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "nombre_abonnements"],
          key: "nombre_abonnements_month",
          render: (_: string, record: any) => record?.last_month?.nombre_abonnements || 0,
        },
        {
          title: t("manage_stats.montant") + " (Total)",
          dataIndex: ["all_times", "montant_total"],
          key: "montant_total_all",
          render: (_: string, record: any) => `${record?.all_times?.montant_total?.toFixed(2) || 0} DT`
        },
        {
          title: t("manage_stats.montant") + ` (${getLastMonthName()})`,
          dataIndex: ["last_month", "montant_total"],
          key: "montant_total_month",
          render: (_: string, record: any) => `${record?.last_month?.montant_total?.toFixed(2) || 0} DT`
        },
      ],
    },
  };

  const loadDefaultData = (statisticType: string) => {
    if (statisticType === "1") {
      dispatch(getSubscribersByTrip({}));
    } else if (statisticType === "2") {
      dispatch(getSubscribersByEstablishment({}));
    } else if (statisticType === "3") {
      dispatch(getSubscribersByLineKmRange({}));
    } else if (statisticType === "4") {
      dispatch(getSubscribersByDiscount({}));
    } else if (statisticType === "5") {
      dispatch(getRevenuesBySalePeriod({}));
    }
  };

  const handleStatisticChange = (value: string) => {
    setSelectedStatistic(value);
    form.resetFields();
    dispatch(clearSubscribersByTrip());
    dispatch(clearSubscribersByEstablishment());
    dispatch(clearSubscribersByLineKmRange());
    dispatch(clearSubscribersByDiscount());
    dispatch(clearRevenuesBySalePeriod());

    if (value) {
      loadDefaultData(value);
    }
  };

  const handleSubmit = async (values: any) => {
    if (!selectedStatistic) return;

    try {
      if (selectedStatistic === "1") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          journey: values.journey,
        };
        dispatch(getSubscribersByTrip(params));
      } else if (selectedStatistic === "2") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          establishment: values.establishment,
        };
        dispatch(getSubscribersByEstablishment(params));
      } else if (selectedStatistic === "3") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
          salePeriod: values.salePeriod,
          lineId: values.lineId,
        };
        dispatch(getSubscribersByLineKmRange(params));
      } else if (selectedStatistic === "4") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getSubscribersByDiscount(params));
      } else if (selectedStatistic === "5") {
        const params = {
          subscriptionType: values.subscriptionType,
          startDate: values.startDate ? dayjs(values.startDate).format('YYYY-MM-DD') : undefined,
          endDate: values.endDate ? dayjs(values.endDate).format('YYYY-MM-DD') : undefined,
        };
        dispatch(getRevenuesBySalePeriod(params));
      }
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  };


  const flattenLineKmData = () => {
    if (!subscribersByLineKmRange?.data?.length) return [];

    const flatData: any[] = [];
    subscribersByLineKmRange.data.forEach((line: any) => {
      // Utiliser all_times pour le tableau
      line.all_times?.kilometric_ranges?.forEach((range: any) => {
        flatData.push({
          line_id: line.line_id,
          line_name_fr: line.line_name_fr,
          line_name_en: line.line_name_en,
          line_name_ar: line.line_name_ar,
          line_code: line.line_code,
          kilometric_range: range.kilometric_range,
          km_range_start: range.km_range_start,
          km_range_end: range.km_range_end,
          all_times: {
            subscriber_count: range.subscriber_count,
            total_amount: range.total_amount,
            average_amount_per_subscriber: range.average_amount_per_subscriber,
          },
          // Ajouter aussi les données last_month si disponibles
          last_month: line.last_month?.kilometric_ranges?.find((r: any) =>
            r.km_range_start === range.km_range_start && r.km_range_end === range.km_range_end
          ) || {
            subscriber_count: 0,
            total_amount: 0,
            average_amount_per_subscriber: 0,
          }
        });
      });
    });
    return flatData;
  };

  const calculateLineKmSummaryStats = () => {
    if (!subscribersByLineKmRange?.summary) {
      return {
        totalSubscribers: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByLineKmRange.summary.all_times;
    return {
      totalSubscribers: summary.grand_total_subscribers || 0,
      totalAmount: summary.grand_total_amount || 0,
      totalItems: summary.total_lines || 0,
      averagePerItem: summary.average_amount_per_line || 0
    };
  };

  const prepareLineKmChartData = () => {
    const flatData = flattenLineKmData();
    return flatData.map((item: any) => ({
      nom: `${item[`line_name_${currentLang}`] || item.line_code} - ${item.kilometric_range}`,
      abonnes: item.last_month?.subscriber_count || 0,
      montant: item.last_month?.total_amount || 0,
    }));
  };

  const calculateDiscountSummaryStats = () => {
    if (!subscribersByDiscount?.summary) {
      return {
        totalSubscribers: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = subscribersByDiscount.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_discounts || 0,
      averagePerItem: summary.total_discounts > 0 ? (summary.total_montant || 0) / summary.total_discounts : 0
    };
  };

  const prepareDiscountChartData = () => {
    if (!subscribersByDiscount?.data?.length) return [];
    console.log("subscribersByDiscount", subscribersByDiscount.data);
    return subscribersByDiscount.data.map((item: any) => ({
      nom: `${item[`nom_${currentLang}`] || item.nom_fr || 'Remise'} (${item.percentage}%)`,
      abonnes: item.last_month?.nombre_abonnements || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const calculateSalePeriodSummaryStats = () => {
    if (!revenuesBySalePeriod?.summary) {
      return {
        totalSubscribers: 0,
        totalAmount: 0,
        totalItems: 0,
        averagePerItem: 0
      };
    }

    const summary = revenuesBySalePeriod.summary.all_times;
    return {
      totalSubscribers: summary.total_abonnements || 0,
      totalAmount: summary.total_montant || 0,
      totalItems: summary.total_sale_periods || 0,
      averagePerItem: summary.total_sale_periods > 0 ? (summary.total_montant || 0) / summary.total_sale_periods : 0
    };
  };

  const prepareSalePeriodChartData = () => {
    if (!revenuesBySalePeriod?.data?.length) return [];

    return revenuesBySalePeriod.data.map((item: any) => ({
      nom: `${item[`sale_period_nom_${currentLang}`] || item.sale_period_nom_fr || 'Période'}`,
      abonnes: item.last_month?.nombre_abonnements || 0,
      montant: item.last_month?.montant_total || 0,
    }));
  };

  const currentConfig = selectedStatistic
    ? STATISTICS_CONFIG[selectedStatistic]
    : null;
  const breadcrumbItems = [
    {
      title: (
        <Link to="/auth/dashboard">
          {t("auth_sidebar.dashboard")}
        </Link>
      ),
    },
    {
      title: t("manage_stats.title"),
    },
  ];

  const summaryStats = calculateSummaryStats();
  const chartData = prepareChartData();

  const getColumnConfig = () => {
    if (!chartData || chartData.length === 0) {
      return {
        data: [{ type: t("common.loading"), abonnes: 0 }],
        xField: 'type',
        yField: 'abonnes',
        height: 300,
        color: '#BC0202',
        columnStyle: {
          radius: [6, 6, 0, 0],
          fillOpacity: 0.8,
        },
        maxColumnWidth: 30,
      };
    }

    const data = [...chartData]
      .sort((a: any, b: any) => a.nom.localeCompare(b.nom))
      .map((item: any, index: number) => {
        return {
          type: item.nom,
          abonnes: item.abonnes,
          index: index
        };
      });

    return {
      data,
      xField: 'type',
      yField: 'abonnes',
      height: 300,
      color: '#BC0202',
      columnStyle: {
        radius: [6, 6, 0, 0],
        fillOpacity: 0.8,
      },
      columnWidthRatio: 0.6,
      maxColumnWidth: 30,
    };
  };

  const columnConfig = getColumnConfig();

  const getPieConfig = () => {
    if (!chartData || chartData.length === 0) {
      return {
        data: [{ type: t("common.loading"), value: 0 }],
        angleField: 'value',
        colorField: 'type',
        legend: true,
        state: {
          inactive: { opacity: 0.5 },
          active: { opacity: 1 },
        },
        radius: 0.8,
        height: 300,
        style: { stroke: '#fff', lineWidth: 2 },
      };
    }

    return {
      data: chartData,
      angleField: 'abonnes',
      colorField: 'nom',
      label: {
        text: (d: any) => `${d.nom} (${d.abonnes})`,
        position: 'spider',
      },
      interaction: {
        elementHighlight: true,
      },
      state: {
        inactive: { opacity: 0.5 },
        active: { opacity: 1 },
      },
      legend: true,
      radius: 0.8,
      height: 300,
      style: { stroke: '#fff', lineWidth: 2 },
      theme: {
        colors10: ['#BC0202', '#10B981', '#3B82F6', '#8B5CF6', '#F59E0B', '#EF4444', '#06B6D4', '#84CC16', '#F97316', '#EC4899'],
      },
    };
  };

  const pieConfig = getPieConfig();

  const getLineConfig = () => {
    const getFieldLabels = () => {
      if (selectedStatistic === "1") {
        return { xField: 'trajet', yField: 'montant', xLabel: t("manage_stats.trajet") };
      } else if (selectedStatistic === "2") {
        return { xField: 'etablissement', yField: 'montant', xLabel: t("manage_stats.etab") };
      } else if (selectedStatistic === "3") {
        return { xField: 'ligne', yField: 'montant', xLabel: t("manage_stats.ligne") };
      } else if (selectedStatistic === "4") {
        return { xField: 'remise', yField: 'montant', xLabel: t("manage_stats.remise") };
      } else if (selectedStatistic === "5") {
        return { xField: 'periode', yField: 'montant', xLabel: t("manage_stats.periode_vente") };
      }
      return { xField: 'item', yField: 'montant', xLabel: 'Item' };
    };

    const { xField, yField, xLabel } = getFieldLabels();

    if (!chartData || chartData.length === 0) {
      return {
        data: [{ [xField]: t("common.loading"), [yField]: 0 }],
        xField,
        yField,
        height: 300,
        color: '#BC0202',
        point: {
          size: 5,
          shape: 'diamond',
          fill: '#BC0202',
        },
      };
    }

    const data = [...chartData]
      .sort((a: any, b: any) => a.nom.localeCompare(b.nom))
      .map((item: any, index: number) => {
        return {
          [xField]: item.nom,
          [yField]: item.montant,
          index: index
        };
      });

    return {
      data,
      xField,
      yField,
      height: 300,
      smooth: true,
      color: '#BC0202',
      point: {
        size: 5,
        shape: 'diamond',
        fill: '#BC0202',
      },
    };
  };

  const lineConfig = getLineConfig();

  return (
    <div className="min-h-screen">
      <div className="mx-auto p-6">
        <Breadcrumb className="mb-6" items={breadcrumbItems} />

        {/* Section de sélection du type de statistique - Centré en haut */}
        <div className="text-center mb-2">
          <div className="bg-white rounded border border-gray-200 p-8">
            <div className="flex flex-col items-center justify-center bg-white">
            <div className="text-3xl">📈</div>
            <Title level={2} className="mb-0 text-gray-800 text-center">
              {t("manage_stats.paragraph")}
            </Title>
          </div>

            <p className="text-gray-600 mb-6 text-lg">
              Sélectionnez le type de statistique que vous souhaitez analyser
            </p>
            <div className="flex justify-center">
              <Select
                size="large"
                className="w-full max-w-lg"
                placeholder={t("manage_stats.placeholder")}
                onChange={handleStatisticChange}
                value={selectedStatistic}
                style={{ borderRadius: '8px' }}
              >
                {Object.entries(STATISTICS_CONFIG).map(([id, config]) => (
                  <Option key={id} value={id}>
                    {t(config.label)}
                  </Option>
                ))}
              </Select>
            </div>
          </div>
        </div>

        {/* Section de filtrage collapsible */}
        {currentConfig && (
          <div className="bg-white rounded-lg border border-gray-200 mb-2">
            <Collapse
              ghost
              expandIconPosition="end"
              className="bg-white rounded-lg"
            >
              <Panel
                header={
                  <div className="flex items-center py-2">
                    <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                      <FilterOutlined className="text-blue-600" />
                    </div>
                    <span className="font-semibold text-gray-800 text-lg">
                      🔍 Filtres de recherche
                    </span>
                  </div>
                }
                key="filters"
                className="border-0"
              >
                <div className="px-4 pb-4">
                  <Form form={form} onFinish={handleSubmit} layout="vertical">
                    <Row gutter={[16, 16]}>
                      {currentConfig.filters.map((filter: any) => (
                        <Col xs={24} sm={12} md={8} lg={6} key={filter.name}>
                          <Form.Item
                            name={filter.name}
                            label={
                              <span className="font-medium text-gray-700">
                                {t(filter.label)}
                              </span>
                            }
                          >
                            {filter.type === "select" ? (
                              <Select
                                size="large"
                                options={filter.options}
                                placeholder={t(filter.label)}
                                allowClear
                                style={{ borderRadius: '6px' }}
                              />
                            ) : filter.type === "date" ? (
                              <DatePicker
                                size="large"
                                className="w-full"
                                style={{ borderRadius: '6px' }}
                              />
                            ) : (
                              <Input
                                size="large"
                                placeholder={t(filter.label)}
                                style={{ borderRadius: '6px' }}
                              />
                            )}
                          </Form.Item>
                        </Col>
                      ))}
                    </Row>
                    <div className="flex gap-3 mt-6 pt-4 border-t border-gray-100">
                      <Button
                        type="primary"
                        htmlType="submit"
                        loading={loading}
                        size="large"
                        icon={<BarChartOutlined />}
                        className="bg-red-600 hover:bg-red-700 border-red-600 font-medium"
                        style={{ borderRadius: '6px' }}
                      >
                        {t("manage_stats.btn")}
                      </Button>
                      <Button
                        type="default"
                        onClick={() => {
                          form.resetFields();
                          if (selectedStatistic) {
                            loadDefaultData(selectedStatistic);
                          }
                        }}
                        size="large"
                        className="font-medium"
                        style={{ borderRadius: '6px' }}
                      >
                        {t("manage_stats.reset")}
                      </Button>
                    </div>
                  </Form>
                </div>
              </Panel>
            </Collapse>
          </div>
        )}

        <div className="mb-6">
          <Row gutter={[20, 20]}>
            {/* Carte Total Abonnés */}
            <Col xs={24} sm={12} lg={6}>
              <div
                className="relative bg-gradient-to-br from-red-50 to-red-100 rounded border border-red-200 p-6
                          hover:from-red-100 hover:to-red-200 hover:border-red-300
                          transition-all duration-300 cursor-pointer group overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%)',

                }}
              >
                <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-30 transition-opacity">
                  <UserOutlined style={{ fontSize: '32px', color: '#BC0202' }} />
                </div>
                <div className="relative z-10">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-red-600 rounded-lg flex items-center justify-center mr-3">
                      <UserOutlined className="text-white text-lg" />
                    </div>
                    <span className="text-gray-700 font-semibold text-sm">
                      {t("manage_stats.total_abonnees")}
                    </span>
                  </div>
                  <div className="text-3xl font-bold text-red-600 mb-1">
                    {summaryStats.totalSubscribers.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    Abonnés actifs
                  </div>
                </div>
              </div>
            </Col>

            {/* Carte Total Montant */}
            <Col xs={24} sm={12} lg={6}>
              <div
                className="relative bg-gradient-to-br from-green-50 to-green-100 rounded border border-green-200 p-6
                          hover:from-green-100 hover:to-green-200 hover:border-green-300
                          transition-all duration-300 cursor-pointer group overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%)',
                }}
              >
                <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-30 transition-opacity">
                  <DollarOutlined style={{ fontSize: '32px', color: '#16a34a' }} />
                </div>
                <div className="relative z-10">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center mr-3">
                      <DollarOutlined className="text-white text-lg" />
                    </div>
                    <span className="text-gray-700 font-semibold text-sm">
                      {t("manage_stats.total_montant")}
                    </span>
                  </div>
                  <div className="text-3xl font-bold text-green-600 mb-1">
                    {summaryStats.totalAmount.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} DT
                  </div>
                  <div className="text-xs text-gray-500">
                    Revenus générés
                  </div>
                </div>
              </div>
            </Col>

            {/* Carte Total Items */}
            <Col xs={24} sm={12} lg={6}>
              <div
                className="relative bg-gradient-to-br from-blue-50 to-blue-100 rounded border border-blue-200 p-6
                          hover:from-blue-100 hover:to-blue-200 hover:border-blue-300
                          transition-all duration-300 cursor-pointer group overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%)',
                }}
              >
                <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-30 transition-opacity">
                  <DatabaseOutlined style={{ fontSize: '32px', color: '#2563eb' }} />
                </div>
                <div className="relative z-10">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center mr-3">
                      <DatabaseOutlined className="text-white text-lg" />
                    </div>
                    <span className="text-gray-700 font-semibold text-sm">
                          {selectedStatistic === "1" ? t("manage_stats.total_trajets") :
                           selectedStatistic === "2" ? t("manage_stats.total_etablissements") :
                           selectedStatistic === "3" ? t("manage_stats.total_lignes") :
                           selectedStatistic === "4" ? t("manage_stats.total_remises") :
                           t("manage_stats.total_periodes")}
                    </span>
                  </div>
                  <div className="text-3xl font-bold text-blue-600 mb-1">
                    {summaryStats.totalItems}
                  </div>
                  <div className="text-xs text-gray-500">
                    {selectedStatistic === "1" ? "Trajets disponibles" :
                     selectedStatistic === "2" ? "Établissements" :
                     selectedStatistic === "3" ? "Lignes actives" :
                     selectedStatistic === "4" ? "Remises disponibles" :
                     "Périodes de vente"}
                  </div>
                </div>
              </div>
            </Col>

            {/* Carte Moyenne */}
            <Col xs={24} sm={12} lg={6}>
              <div
                className="relative bg-gradient-to-br from-purple-50 to-purple-100 rounded border border-purple-200 p-6
                          hover:from-purple-100 hover:to-purple-200 hover:border-purple-300
                          transition-all duration-300 cursor-pointer group overflow-hidden"
                style={{
                  background: 'linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%)',
                }}
              >
                <div className="absolute top-4 right-4 opacity-20 group-hover:opacity-30 transition-opacity">
                  <TrophyOutlined style={{ fontSize: '32px', color: '#9333ea' }} />
                </div>
                <div className="relative z-10">
                  <div className="flex items-center mb-3">
                    <div className="w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center mr-3">
                      <TrophyOutlined className="text-white text-lg" />
                    </div>
                    <span className="text-gray-700 font-semibold text-sm">
                         {selectedStatistic === "1" ? t("manage_stats.moyenne_trajet") :
                           selectedStatistic === "2" ? t("manage_stats.moyenne_etablissement") :
                           selectedStatistic === "3" ? t("manage_stats.moyenne_ligne") :
                           selectedStatistic === "4" ? t("manage_stats.moyenne_remise") :
                           t("manage_stats.moyenne_periode")}
                    </span>
                  </div>
                  <div className="text-3xl font-bold text-purple-600 mb-1">
                    {summaryStats.averagePerItem.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })} DT
                  </div>
                  <div className="text-xs text-gray-500">
                    Moyenne par élément
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </div>



        {/* Section des graphiques et tableau */}
        <div className="space-y-6">
          {/* Graphiques */}
          {!loading && ((selectedStatistic === "1" && subscribersByTrip?.data?.length > 0) ||
                        (selectedStatistic === "2" && subscribersByEstablishment?.data?.length > 0) ||
                        (selectedStatistic === "3" && subscribersByLineKmRange?.data?.length > 0) ||
                        (selectedStatistic === "4" && subscribersByDiscount?.data?.length > 0) ||
                        (selectedStatistic === "5" && revenuesBySalePeriod?.data?.length > 0)) && (
            <Row gutter={[24, 24]}>
              <Col xs={24} lg={12}>
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className="w-8 h-8 bg-red-100 rounded-lg flex items-center justify-center mr-3">
                          <BarChartOutlined className="text-red-600 text-sm" />
                        </div>
                        <div>
                          <h3 className="text-gray-800 font-semibold text-base mb-1">
                             {t("manage_stats.nb_abonnees")}
                             <span className="text-xs text-blue-600 ml-2">({getLastMonthName()})</span>
                          </h3>
                          <p className="text-gray-500 text-xs">
                            {selectedStatistic === "1" ? t("manage_stats.trajet") :
                             selectedStatistic === "2" ? t("manage_stats.etab") :
                             selectedStatistic === "3" ? t("manage_stats.ligne_tranch_km") :
                             selectedStatistic === "4" ? t("manage_stats.remise") :
                             t("manage_stats.periode_vente")}
                             <span className="text-blue-600"> - Données de {getLastMonthName()}</span>
                          </p>
                        </div>
                      </div>
                      <Select
                        value={chartType}
                        onChange={setChartType}
                        size="small"
                        className="min-w-[130px]"
                        style={{ borderRadius: '6px' }}
                      >
                        <Option value="column">
                          <div className="flex items-center">
                            <BarChartOutlined className="mr-2 text-gray-600" />
                            <span>Colonnes</span>
                          </div>
                        </Option>
                        <Option value="pie">
                          <div className="flex items-center">
                            <PieChartOutlined className="mr-2 text-gray-600" />
                            <span>Secteurs</span>
                          </div>
                        </Option>
                        <Option value="line">
                          <div className="flex items-center">
                            <LineChartOutlined className="mr-2 text-gray-600" />
                            <span>Ligne</span>
                          </div>
                        </Option>
                      </Select>
                    </div>
                  </div>
                  <div className="p-6">
                    <div style={{ height: 320 }}>
                      {chartType === 'column' && <Column {...columnConfig} />}
                      {chartType === 'pie' && <Pie {...pieConfig} />}
                      {chartType === 'line' && <Line {...lineConfig} />}
                    </div>
                  </div>
                </div>
              </Col>

              <Col xs={24} lg={12}>
                <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
                  <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
                    <div className="flex items-center">
                      <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                        <LineChartOutlined className="text-green-600 text-sm" />
                      </div>
                      <div>
                        <h3 className="text-gray-800 font-semibold text-base mb-1">
                          {t("manage_stats.montant")}
                          <span className="text-xs text-blue-600 ml-2">({getLastMonthName()})</span>
                        </h3>
                        <p className="text-gray-500 text-xs">
                          {selectedStatistic === "1" ? t("manage_stats.trajet") :
                           selectedStatistic === "2" ? t("manage_stats.etab") :
                           selectedStatistic === "3" ? t("manage_stats.ligne_tranch_km") :
                           selectedStatistic === "4" ? t("manage_stats.remise") :
                           t("manage_stats.periode_vente")}
                           <span className="text-blue-600"> - Données de {getLastMonthName()}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div style={{ height: 320 }}>
                      <Line {...lineConfig} />
                    </div>
                  </div>
                </div>
              </Col>
            </Row>
          )}

          {/* Tableau des données */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-100 bg-gray-50">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                  <SettingOutlined className="text-blue-600 text-sm" />
                </div>
                <div>
                  <h3 className="text-gray-800 font-semibold text-base mb-1">
                    📋 {t("manage_stats.donnees_detaillees")}
                    <span className="text-xs text-green-600 ml-2">(Toutes les données)</span>
                  </h3>
                  <p className="text-gray-500 text-xs">
                    Tableau complet des données statistiques
                    <span className="text-green-600"> - Données totales et de {getLastMonthName()}</span>
                  </p>
                </div>
              </div>
            </div>
            <div className="p-6">
              {loading ? (
                <div className="text-center py-16">
                  <div className="inline-flex flex-col items-center">
                    <Spin size="large" />
                    <div className="mt-4 text-gray-500 font-medium">{t("common.loading")}</div>
                    <div className="mt-2 text-gray-400 text-sm">Chargement des données...</div>
                  </div>
                </div>
              ) : (
                <div className="overflow-hidden rounded-lg border border-gray-100">
                  <Table
                    columns={currentConfig?.columns}
                    dataSource={selectedStatistic === "1" ? subscribersByTrip?.data :
                               selectedStatistic === "2" ? subscribersByEstablishment?.data :
                               selectedStatistic === "3" ? flattenLineKmData() :
                               selectedStatistic === "4" ? subscribersByDiscount?.data :
                               revenuesBySalePeriod?.data}
                    rowKey={selectedStatistic === "1" ? "trip_id" :
                            selectedStatistic === "2" ? "establishment_id" :
                            selectedStatistic === "3" ? (record: any) => `${record.line_id}-${record.km_range_start}` :
                            selectedStatistic === "4" ? "discount_id" :
                            "sale_period_id"}
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showQuickJumper: true,
                      showTotal: (total, range) =>
                        `${range[0]}-${range[1]} sur ${total} éléments`,
                      className: "px-4 py-3 bg-gray-50 border-t border-gray-100"
                    }}
                    scroll={{ x: 800 }}
                    className="border-0"
                    size="middle"
                  />
                </div>
              )}
            </div>
          </div>
        </div>

      </div>
    </div>
  );
};

export default ManageStats;
